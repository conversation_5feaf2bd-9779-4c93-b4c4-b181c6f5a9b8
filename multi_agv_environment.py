"""
基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化实现
Multi-AGV Environment Framework - 环境框架设计与实现

Author: 张超
Date: 2024-12
Description: 实现26×10网格世界环境，包含AGV管理器、任务管理器和碰撞检测器
"""

import numpy as np
import gym
from gym import spaces
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from dataclasses import dataclass
from enum import Enum
import random


class TaskStatus(Enum):
    """任务状态枚举"""
    AVAILABLE = 0      # 未分配
    ASSIGNED = 1       # 已分配
    IN_TRANSPORT = 2   # 运输中
    COMPLETED = 3      # 已完成


class ActionType(Enum):
    """动作类型枚举"""
    # 运动控制动作
    UP = 0
    DOWN = 1
    LEFT = 2
    RIGHT = 3
    STAY = 4
    
    # 任务选择动作（0-15为任务ID，16为无任务，17为卸货）
    NO_TASK = 16
    UNLOAD = 17


@dataclass
class AGVState:
    """AGV状态数据类"""
    id: int
    position: Tuple[int, int]
    current_load: float
    max_capacity: float
    carrying_tasks: List[int]
    target_unload_point: int  # 0: 左上角(0,0), 1: 右上角(25,0)
    
    @property
    def load_utilization(self) -> float:
        """载重利用率"""
        return self.current_load / self.max_capacity if self.max_capacity > 0 else 0.0
    
    @property
    def remaining_capacity(self) -> float:
        """剩余载重容量"""
        return self.max_capacity - self.current_load


@dataclass
class Task:
    """任务数据类"""
    id: int
    position: Tuple[int, int]
    weight: float
    target_unload_point: int  # 0: 左上角(0,0), 1: 右上角(25,0)
    status: TaskStatus
    assigned_agv: Optional[int] = None
    
    @property
    def is_available(self) -> bool:
        """任务是否可用"""
        return self.status == TaskStatus.AVAILABLE


class GridWorld:
    """26×10网格世界类"""
    
    def __init__(self, width: int = 26, height: int = 10):
        self.width = width
        self.height = height
        self.grid = np.zeros((height, width), dtype=int)

        # 设置卸货点
        self.unload_points = [(0, 0), (25, 0)]  # 左上角和右上角

        # 设置货架位置（16个货架）
        self.shelf_positions = self._generate_shelf_positions()
        self._place_shelves()
        
    def _generate_shelf_positions(self) -> List[Tuple[int, int]]:
        """生成16个货架位置"""
        positions = []
        # 在网格中均匀分布16个货架，避开卸货点
        for i in range(4):
            for j in range(4):
                x = 3 + j * 5  # 避开边界，均匀分布
                y = 2 + i * 2  # 避开上边界的卸货点
                if (x, y) not in self.unload_points:
                    positions.append((x, y))
        return positions[:16]  # 确保只有16个货架
    
    def _place_shelves(self):
        """在网格中放置货架"""
        for x, y in self.shelf_positions:
            self.grid[y, x] = 1  # 1表示货架/障碍物
    
    def is_valid_position(self, position: Tuple[int, int]) -> bool:
        """检查位置是否有效（在边界内且不是障碍物）"""
        x, y = position
        if x < 0 or x >= self.width or y < 0 or y >= self.height:
            return False
        return self.grid[y, x] == 0  # 0表示空地
    
    def get_neighbors(self, position: Tuple[int, int]) -> List[Tuple[int, int]]:
        """获取位置的有效邻居"""
        x, y = position
        neighbors = []
        for dx, dy in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
            new_pos = (x + dx, y + dy)
            if self.is_valid_position(new_pos):
                neighbors.append(new_pos)
        return neighbors


class AGVManager:
    """AGV管理器类"""
    
    def __init__(self, n_agvs: int = 4, max_capacity: float = 25.0):
        self.n_agvs = n_agvs
        self.max_capacity = max_capacity
        self.agvs = self._initialize_agvs()
        
    def _initialize_agvs(self) -> List[AGVState]:
        """初始化AGV状态"""
        agvs = []
        # AGV初始位置分布在网格的四个角落附近
        start_positions = [(1, 1), (24, 1), (1, 8), (24, 8)]
        
        for i in range(self.n_agvs):
            agv = AGVState(
                id=i,
                position=start_positions[i % len(start_positions)],
                current_load=0.0,
                max_capacity=self.max_capacity,
                carrying_tasks=[],
                target_unload_point=0  # 默认目标卸货点
            )
            agvs.append(agv)
        return agvs
    
    def move_agv(self, agv_id: int, action: int, grid_world: GridWorld) -> bool:
        """移动AGV，返回是否移动成功"""
        if agv_id >= len(self.agvs):
            return False
            
        agv = self.agvs[agv_id]
        x, y = agv.position
        
        # 根据动作计算新位置
        if action == ActionType.UP.value:
            new_pos = (x, y - 1)
        elif action == ActionType.DOWN.value:
            new_pos = (x, y + 1)
        elif action == ActionType.LEFT.value:
            new_pos = (x - 1, y)
        elif action == ActionType.RIGHT.value:
            new_pos = (x + 1, y)
        else:  # STAY
            new_pos = (x, y)
        
        # 检查新位置是否有效
        if grid_world.is_valid_position(new_pos):
            agv.position = new_pos
            return True
        return False
    
    def assign_task(self, agv_id: int, task_id: int, task_weight: float) -> bool:
        """为AGV分配任务"""
        if agv_id >= len(self.agvs):
            return False
            
        agv = self.agvs[agv_id]
        if agv.remaining_capacity >= task_weight:
            agv.carrying_tasks.append(task_id)
            agv.current_load += task_weight
            return True
        return False
    
    def unload_tasks(self, agv_id: int) -> List[int]:
        """AGV卸载所有任务"""
        if agv_id >= len(self.agvs):
            return []
            
        agv = self.agvs[agv_id]
        completed_tasks = agv.carrying_tasks.copy()
        agv.carrying_tasks.clear()
        agv.current_load = 0.0
        return completed_tasks
    
    def get_agv_positions(self) -> List[Tuple[int, int]]:
        """获取所有AGV位置"""
        return [agv.position for agv in self.agvs]


class TaskManager:
    """任务管理器类"""
    
    def __init__(self, n_tasks: int = 16, shelf_positions: List[Tuple[int, int]] = None):
        self.n_tasks = n_tasks
        self.shelf_positions = shelf_positions or []
        self.tasks = self._initialize_tasks()
        
    def _initialize_tasks(self) -> List[Task]:
        """初始化任务"""
        tasks = []
        for i in range(self.n_tasks):
            # 任务位置对应货架位置
            position = self.shelf_positions[i] if i < len(self.shelf_positions) else (0, 0)
            
            task = Task(
                id=i,
                position=position,
                weight=random.uniform(1.0, 15.0),  # 随机重量1-15单位
                target_unload_point=random.choice([0, 1]),  # 随机目标卸货点
                status=TaskStatus.AVAILABLE
            )
            tasks.append(task)
        return tasks
    
    def assign_task(self, task_id: int, agv_id: int) -> bool:
        """分配任务给AGV"""
        if task_id >= len(self.tasks):
            return False
            
        task = self.tasks[task_id]
        if task.is_available:
            task.status = TaskStatus.ASSIGNED
            task.assigned_agv = agv_id
            return True
        return False
    
    def pickup_task(self, task_id: int) -> bool:
        """AGV拾取任务"""
        if task_id >= len(self.tasks):
            return False
            
        task = self.tasks[task_id]
        if task.status == TaskStatus.ASSIGNED:
            task.status = TaskStatus.IN_TRANSPORT
            return True
        return False
    
    def complete_task(self, task_id: int) -> bool:
        """完成任务"""
        if task_id >= len(self.tasks):
            return False
            
        task = self.tasks[task_id]
        if task.status == TaskStatus.IN_TRANSPORT:
            task.status = TaskStatus.COMPLETED
            return True
        return False
    
    def get_available_tasks(self) -> List[int]:
        """获取可用任务ID列表"""
        return [task.id for task in self.tasks if task.is_available]
    
    def get_task_info(self, task_id: int) -> Optional[Task]:
        """获取任务信息"""
        if task_id < len(self.tasks):
            return self.tasks[task_id]
        return None


class CollisionDetector:
    """碰撞检测器类"""
    
    def __init__(self):
        pass
    
    def detect_agv_collisions(self, agv_positions: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """检测AGV间的碰撞，返回碰撞的AGV对"""
        collisions = []
        for i in range(len(agv_positions)):
            for j in range(i + 1, len(agv_positions)):
                if agv_positions[i] == agv_positions[j]:
                    collisions.append((i, j))
        return collisions
    
    def detect_obstacle_collision(self, position: Tuple[int, int], grid_world: GridWorld) -> bool:
        """检测与障碍物的碰撞"""
        return not grid_world.is_valid_position(position)
    
    def predict_collision(self, current_positions: List[Tuple[int, int]], 
                         next_positions: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """预测性碰撞检测"""
        # 检测下一步是否会发生碰撞
        return self.detect_agv_collisions(next_positions)


class MultiAGVEnvironment(gym.Env):
    """多AGV环境主类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__()
        
        # 默认配置
        self.config = config or {
            'n_agvs': 4,
            'n_tasks': 16,
            'max_capacity': 25.0,
            'grid_width': 26,
            'grid_height': 10,
            'max_steps': 500
        }
        
        # 初始化环境组件
        self.grid_world = GridWorld(self.config['grid_width'], self.config['grid_height'])
        self.agv_manager = AGVManager(self.config['n_agvs'], self.config['max_capacity'])
        self.task_manager = TaskManager(self.config['n_tasks'], self.grid_world.shelf_positions)
        self.collision_detector = CollisionDetector()
        
        # 环境状态
        self.current_step = 0
        self.max_steps = self.config['max_steps']
        self.total_collisions = 0
        self.completed_tasks = 0
        
        # 定义观测空间和动作空间
        self._define_spaces()
        
    def _define_spaces(self):
        """定义观测空间和动作空间"""
        # 观测空间：AGV局部观测(5) + 任务全局状态(64) + 环境全局状态(8) = 77维
        obs_dim = 5 + self.config['n_tasks'] * 4 + 8
        self.observation_space = spaces.Box(
            low=0.0, high=1.0, shape=(obs_dim,), dtype=np.float32
        )
        
        # 动作空间：每个AGV有双头动作（任务选择18维 + 运动控制5维）
        self.action_space = spaces.Dict({
            f'agv_{i}': spaces.Dict({
                'task_action': spaces.Discrete(18),  # 16任务 + 无任务 + 卸货
                'motion_action': spaces.Discrete(5)   # 上下左右静止
            }) for i in range(self.config['n_agvs'])
        })
    
    def reset(self) -> Dict[str, np.ndarray]:
        """重置环境"""
        # 重新初始化所有组件
        self.agv_manager = AGVManager(self.config['n_agvs'], self.config['max_capacity'])
        self.task_manager = TaskManager(self.config['n_tasks'], self.grid_world.shelf_positions)
        
        # 重置环境状态
        self.current_step = 0
        self.total_collisions = 0
        self.completed_tasks = 0
        
        return self._get_observations()
    
    def step(self, actions: Dict[str, Dict[str, int]]) -> Tuple[Dict[str, np.ndarray], Dict[str, float], bool, Dict[str, Any]]:
        """执行一步环境交互"""
        # 解析动作
        task_actions, motion_actions = self._parse_actions(actions)
        
        # 执行任务选择
        self._execute_task_actions(task_actions)
        
        # 执行运动控制
        self._execute_motion_actions(motion_actions)
        
        # 碰撞检测和处理
        self._handle_collisions()
        
        # 更新环境状态
        self._update_environment_state()
        
        # 计算奖励
        rewards = self._compute_rewards()
        
        # 检查终止条件
        done = self._check_termination()
        
        # 更新步数
        self.current_step += 1
        
        return self._get_observations(), rewards, done, self._get_info()
    
    def _parse_actions(self, actions: Dict[str, Dict[str, int]]) -> Tuple[List[int], List[int]]:
        """解析动作"""
        task_actions = []
        motion_actions = []
        
        for i in range(self.config['n_agvs']):
            agv_key = f'agv_{i}'
            if agv_key in actions:
                task_actions.append(actions[agv_key]['task_action'])
                motion_actions.append(actions[agv_key]['motion_action'])
            else:
                task_actions.append(ActionType.NO_TASK.value)
                motion_actions.append(ActionType.STAY.value)
        
        return task_actions, motion_actions
    
    def _execute_task_actions(self, task_actions: List[int]):
        """执行任务选择动作"""
        for agv_id, task_action in enumerate(task_actions):
            if task_action < 16:  # 选择任务
                task_info = self.task_manager.get_task_info(task_action)
                if task_info and task_info.is_available:
                    # 检查AGV是否在任务位置且有足够载重
                    agv = self.agv_manager.agvs[agv_id]
                    if (agv.position == task_info.position and 
                        agv.remaining_capacity >= task_info.weight):
                        # 分配并拾取任务
                        if self.task_manager.assign_task(task_action, agv_id):
                            if self.task_manager.pickup_task(task_action):
                                self.agv_manager.assign_task(agv_id, task_action, task_info.weight)
            
            elif task_action == ActionType.UNLOAD.value:  # 卸货动作
                agv = self.agv_manager.agvs[agv_id]
                unload_point = self.grid_world.unload_points[agv.target_unload_point]
                if agv.position == unload_point:
                    # 执行卸货
                    completed_task_ids = self.agv_manager.unload_tasks(agv_id)
                    for task_id in completed_task_ids:
                        self.task_manager.complete_task(task_id)
                        self.completed_tasks += 1
    
    def _execute_motion_actions(self, motion_actions: List[int]):
        """执行运动控制动作"""
        for agv_id, motion_action in enumerate(motion_actions):
            self.agv_manager.move_agv(agv_id, motion_action, self.grid_world)
    
    def _handle_collisions(self):
        """处理碰撞"""
        agv_positions = self.agv_manager.get_agv_positions()
        collisions = self.collision_detector.detect_agv_collisions(agv_positions)
        self.total_collisions += len(collisions)
        
        # 简单的碰撞处理：将碰撞的AGV移回上一个位置（这里简化处理）
        # 实际实现中可能需要更复杂的碰撞解决策略
    
    def _update_environment_state(self):
        """更新环境状态"""
        # 更新AGV的目标卸货点（基于当前携带的任务）
        for agv in self.agv_manager.agvs:
            if agv.carrying_tasks:
                # 选择第一个任务的目标卸货点
                first_task = self.task_manager.get_task_info(agv.carrying_tasks[0])
                if first_task:
                    agv.target_unload_point = first_task.target_unload_point
    
    def _compute_rewards(self) -> Dict[str, float]:
        """计算奖励（简化版本）"""
        rewards = {}
        for i in range(self.config['n_agvs']):
            # 基础奖励计算（后续会在奖励函数模块中详细实现）
            reward = 0.0
            
            # 任务完成奖励
            agv = self.agv_manager.agvs[i]
            reward += len(agv.carrying_tasks) * 10.0  # 携带任务奖励
            
            # 载重利用率奖励
            reward += agv.load_utilization * 5.0
            
            # 碰撞惩罚
            reward -= self.total_collisions * 10.0
            
            rewards[f'agv_{i}'] = reward
        
        return rewards
    
    def _check_termination(self) -> bool:
        """检查终止条件"""
        # 所有任务完成或达到最大步数
        all_completed = all(task.status == TaskStatus.COMPLETED for task in self.task_manager.tasks)
        max_steps_reached = self.current_step >= self.max_steps
        
        return all_completed or max_steps_reached
    
    def _get_observations(self) -> Dict[str, np.ndarray]:
        """获取观测（简化版本）"""
        observations = {}
        
        for i in range(self.config['n_agvs']):
            # AGV局部观测（5维）
            agv = self.agv_manager.agvs[i]
            agv_obs = np.array([
                agv.position[0] / 25.0,  # x位置归一化
                agv.position[1] / 9.0,   # y位置归一化
                agv.load_utilization,    # 载重比例
                agv.target_unload_point, # 目标卸货点
                len(agv.carrying_tasks) / 10.0  # 携带任务数归一化
            ], dtype=np.float32)
            
            # 任务全局状态（64维：16任务×4维）
            task_obs = []
            for task in self.task_manager.tasks:
                task_obs.extend([
                    task.position[0] / 25.0,  # 任务x位置
                    task.position[1] / 9.0,   # 任务y位置
                    task.weight / 15.0,       # 任务重量归一化
                    task.status.value / 3.0   # 任务状态归一化
                ])
            task_obs = np.array(task_obs, dtype=np.float32)
            
            # 环境全局状态（8维）
            env_obs = np.array([
                self.completed_tasks / self.config['n_tasks'],  # 完成任务比例
                len(self.task_manager.get_available_tasks()) / self.config['n_tasks'],  # 可用任务比例
                sum(agv.load_utilization for agv in self.agv_manager.agvs) / self.config['n_agvs'],  # 平均载重利用率
                min(1.0, self.total_collisions / 100.0),  # 碰撞次数归一化
                self.current_step / self.max_steps,  # 时间进度
                self.get_congestion_level(),  # 系统拥堵程度
                self.get_system_efficiency(),  # 整体系统效率
                self.calculate_load_balance()   # 载重均衡度
            ], dtype=np.float32)
            
            # 合并观测
            full_obs = np.concatenate([agv_obs, task_obs, env_obs])
            observations[f'agv_{i}'] = full_obs
        
        return observations
    
    def _get_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            'current_step': self.current_step,
            'completed_tasks': self.completed_tasks,
            'total_collisions': self.total_collisions,
            'agv_positions': self.agv_manager.get_agv_positions(),
            'available_tasks': len(self.task_manager.get_available_tasks())
        }
    
    def render(self, mode='human'):
        """渲染环境（基础版本）"""
        if mode == 'human':
            print(f"Step: {self.current_step}")
            print(f"Completed Tasks: {self.completed_tasks}/{self.config['n_tasks']}")
            print(f"Total Collisions: {self.total_collisions}")
            print("AGV Positions:", self.agv_manager.get_agv_positions())
            print("Available Tasks:", len(self.task_manager.get_available_tasks()))
            print("-" * 50)

    def get_action_mask(self, agv_id: int) -> Dict[str, np.ndarray]:
        """获取动作掩码（智能动作掩码机制的基础）"""
        if agv_id >= len(self.agv_manager.agvs):
            return {'task_mask': np.zeros(18), 'motion_mask': np.ones(5)}

        agv = self.agv_manager.agvs[agv_id]

        # 任务选择掩码
        task_mask = np.zeros(18, dtype=np.float32)

        # 检查每个任务是否可选
        for task_id in range(16):
            task = self.task_manager.get_task_info(task_id)
            if task and task.is_available:
                # 检查载重约束
                if agv.remaining_capacity >= task.weight:
                    # 检查位置约束（AGV是否在任务位置）
                    if agv.position == task.position:
                        task_mask[task_id] = 1.0

        # 无任务选项总是可用
        task_mask[16] = 1.0

        # 卸货选项：只有在卸货点且携带任务时可用
        unload_point = self.grid_world.unload_points[agv.target_unload_point]
        if agv.position == unload_point and agv.carrying_tasks:
            task_mask[17] = 1.0

        # 运动控制掩码
        motion_mask = np.ones(5, dtype=np.float32)
        x, y = agv.position

        # 检查各方向是否可移动
        if y <= 0 or not self.grid_world.is_valid_position((x, y-1)):
            motion_mask[0] = 0.0  # UP
        if y >= self.grid_world.height-1 or not self.grid_world.is_valid_position((x, y+1)):
            motion_mask[1] = 0.0  # DOWN
        if x <= 0 or not self.grid_world.is_valid_position((x-1, y)):
            motion_mask[2] = 0.0  # LEFT
        if x >= self.grid_world.width-1 or not self.grid_world.is_valid_position((x+1, y)):
            motion_mask[3] = 0.0  # RIGHT
        # STAY总是可用

        return {'task_mask': task_mask, 'motion_mask': motion_mask}

    def get_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """计算曼哈顿距离"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])

    def get_optimal_path_length(self, start: Tuple[int, int], end: Tuple[int, int]) -> float:
        """计算最优路径长度（简化版A*算法）"""
        # 简化实现：使用曼哈顿距离作为估计
        return self.get_distance(start, end)

    def calculate_load_balance(self) -> float:
        """计算系统载重均衡度"""
        if not self.agv_manager.agvs:
            return 1.0

        utilizations = [agv.load_utilization for agv in self.agv_manager.agvs]
        mean_util = np.mean(utilizations)

        if mean_util == 0:
            return 1.0

        std_util = np.std(utilizations)
        return max(0.0, 1.0 - std_util / mean_util)

    def get_system_efficiency(self) -> float:
        """计算系统整体效率"""
        if self.current_step == 0:
            return 0.0

        # 效率 = 完成任务数 × 平均载重利用率 / 时间步数
        avg_load_util = np.mean([agv.load_utilization for agv in self.agv_manager.agvs])
        efficiency = (self.completed_tasks * avg_load_util) / self.current_step
        return min(1.0, efficiency)

    def get_congestion_level(self) -> float:
        """计算系统拥堵程度"""
        # 简化实现：基于AGV间的平均距离
        positions = self.agv_manager.get_agv_positions()
        if len(positions) < 2:
            return 0.0

        total_distance = 0
        count = 0
        for i in range(len(positions)):
            for j in range(i+1, len(positions)):
                total_distance += self.get_distance(positions[i], positions[j])
                count += 1

        avg_distance = total_distance / count if count > 0 else 0
        max_distance = self.grid_world.width + self.grid_world.height

        # 距离越小，拥堵程度越高
        congestion = max(0.0, 1.0 - avg_distance / max_distance)
        return congestion


def test_environment():
    """测试环境功能"""
    print("=== 多AGV环境测试 ===")

    # 初始化环境
    env = MultiAGVEnvironment()
    obs = env.reset()

    print("✓ 环境初始化成功!")
    print(f"✓ 观测空间: {env.observation_space}")
    print(f"✓ 动作空间: {list(env.action_space.spaces.keys())}")

    # 显示初始状态
    print(f"\n初始状态:")
    print(f"  AGV位置: {env.agv_manager.get_agv_positions()}")
    print(f"  货架位置: {env.grid_world.shelf_positions[:5]}... (显示前5个)")
    print(f"  卸货点: {env.grid_world.unload_points}")
    print(f"  可用任务数: {len(env.task_manager.get_available_tasks())}")

    # 测试动作掩码
    print(f"\n测试动作掩码:")
    for i in range(2):  # 只测试前2个AGV
        mask = env.get_action_mask(i)
        print(f"  AGV {i} - 任务掩码前5个: {mask['task_mask'][:5]}")
        print(f"  AGV {i} - 运动掩码: {mask['motion_mask']}")

    # 测试多步交互
    print(f"\n测试多步交互:")
    for step in range(3):
        actions = {}
        for i in range(4):
            actions[f'agv_{i}'] = {
                'task_action': 16,  # 无任务
                'motion_action': random.choice([0, 1, 2, 3, 4])  # 随机移动
            }

        obs, rewards, done, info = env.step(actions)
        print(f"  步骤 {step+1}: AGV位置 {info['agv_positions']}, 奖励总和: {sum(rewards.values()):.2f}")

        if done:
            break

    # 测试环境指标
    print(f"\n环境指标测试:")
    print(f"  载重均衡度: {env.calculate_load_balance():.3f}")
    print(f"  系统效率: {env.get_system_efficiency():.3f}")
    print(f"  拥堵程度: {env.get_congestion_level():.3f}")

    print(f"\n✓ 环境测试完成!")


def visualize_grid(env):
    """简单的网格可视化"""
    print("\n=== 网格地图可视化 ===")
    grid = np.zeros((env.grid_world.height, env.grid_world.width), dtype=str)
    grid[:] = '.'  # 空地

    # 标记货架
    for x, y in env.grid_world.shelf_positions:
        grid[y, x] = 'S'  # Shelf

    # 标记卸货点
    for x, y in env.grid_world.unload_points:
        grid[y, x] = 'U'  # Unload

    # 标记AGV
    for i, (x, y) in enumerate(env.agv_manager.get_agv_positions()):
        grid[y, x] = str(i)  # AGV编号

    # 打印网格
    print("图例: . = 空地, S = 货架, U = 卸货点, 0-3 = AGV编号")
    for row in grid:
        print(' '.join(row))


if __name__ == "__main__":
    # 运行测试
    test_environment()

    # 创建环境实例用于可视化
    env = MultiAGVEnvironment()
    env.reset()
    visualize_grid(env)
